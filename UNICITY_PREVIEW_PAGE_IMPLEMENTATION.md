# Unicity Preview Page Implementation

## 概述

本实现将 Unicity 预览功能从 dialog 组件转换为独立的全屏页面，并集成了 supersplat-viewer 的功能，包括相机控制、动画时间轴等高级功能。

## 主要变更

### 1. 新增文件

#### `src/ui/unicity-preview-page.ts`
- 独立的预览页面组件
- 集成相机控制（轨道、飞行、动画模式）
- 动画时间轴控制
- 与 iframe 中的 viewer 通信
- 支持返回编辑界面和发布功能

#### `src/ui/scss/unicity-preview-page.scss`
- 预览页面的样式定义
- 响应式设计支持
- 工具栏、控制面板、预览区域布局

#### `src/ui/page-router.ts`
- 简单的页面路由管理器
- 支持编辑器和预览页面之间的切换
- 页面状态管理

### 2. 修改的文件

#### `src/ui/editor.ts`
- 集成页面路由器
- 添加预览页面实例
- 新增 `show.unicityPreviewPage` 事件处理

#### `src/ui/menu.ts`
- 修改 Unicity 发布菜单项，调用新的预览页面

#### `src/ui/scss/style.scss`
- 导入新的预览页面样式

#### `src/ui/localization.ts`
- 添加相机控制、动画控制相关的本地化字符串
- 支持英文和中文

#### `src/file-handler.ts`
- 新增 `scene.generateUnicityPreviewWithViewer` 函数
- 生成包含完整 viewer 功能的预览 HTML

## 功能特性

### 1. 相机控制
- **轨道模式 (Orbit)**: 围绕焦点旋转查看
- **飞行模式 (Fly)**: 自由飞行浏览
- **动画模式 (Animation)**: 播放预设的相机动画
- **重置功能**: 恢复到初始相机位置

### 2. 动画控制
- 播放/暂停动画
- 时间轴拖拽控制
- 实时时间显示
- 自动检测是否有动画轨道

### 3. 用户界面
- 全屏预览体验
- 侧边控制面板
- 顶部工具栏（返回编辑、发布按钮）
- 响应式设计，支持移动设备

### 4. 页面导航
- 从编辑器无缝切换到预览页面
- 支持返回编辑界面
- 支持直接发布
- 保持发布设置状态

## 技术实现

### 1. 页面路由
使用简单的路由管理器在编辑器和预览页面之间切换，避免了复杂的 SPA 路由框架。

### 2. iframe 通信
预览页面通过 `postMessage` API 与 iframe 中的 viewer 进行双向通信：
- 发送相机模式切换指令
- 接收动画状态更新
- 同步时间轴控制

### 3. Viewer 集成
在预览 HTML 中嵌入完整的 PlayCanvas 应用，包括：
- 相机控制器（轨道、飞行、动画）
- 鼠标/触摸交互
- 动画播放系统
- 实时状态同步

### 4. 样式设计
采用现有的设计系统，保持与编辑器界面的一致性：
- 使用相同的颜色变量
- 统一的按钮和控件样式
- 响应式布局

## 使用方法

1. 在编辑器中加载 3D Gaussian Splat 文件
2. 通过菜单 `File > Publish > Unicity` 进入预览页面
3. 在预览页面中：
   - 使用相机控制按钮切换查看模式
   - 如果有动画，可以播放/暂停并拖拽时间轴
   - 点击"重置"恢复初始视角
   - 点击"返回编辑"回到编辑器
   - 点击"发布"完成发布流程

## 兼容性

- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 响应式设计，支持桌面和移动设备
- 保持与现有 Unicity 发布流程的兼容性

## 未来扩展

1. **更多相机模式**: 可以添加更多的相机控制模式
2. **高级动画控制**: 支持动画速度调节、循环模式等
3. **预览设置**: 允许用户调整预览质量、渲染选项等
4. **分享功能**: 支持生成预览链接分享给他人
5. **VR/AR 支持**: 集成 WebXR 功能

## 实际 Splat 数据加载

### 🎯 **核心功能实现**

现在预览页面已经实现了加载实际的 splat 数据，而不是简单的占位符：

1. **真实数据加载**: 使用 PlayCanvas 的 GSplat 加载器加载实际的 splat 数据
2. **参数应用**: 完全使用 publishSettings 中的参数，包括：
   - 初始相机位置和目标点
   - 背景颜色
   - 相机 FOV
   - 动画轨道设置

### 🔧 **技术实现细节**

#### Splat 数据处理
```javascript
// 将base64编码的splat数据转换为ArrayBuffer
const binaryString = splatData;
const bytes = new Uint8Array(binaryString.length);
for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
}
const splatBuffer = bytes.buffer;

// 使用PlayCanvas的GSplat加载器
const asset = new pc.Asset('preview-splat', 'gsplat', null, splatBuffer);
```

#### 相机设置应用
```javascript
// 应用初始相机设置
if (settings.camera?.position && settings.camera?.target) {
    const pos = settings.camera.position;
    const target = settings.camera.target;

    camera.setPosition(pos[0], pos[1], pos[2]);
    camera.lookAt(target[0], target[1], target[2]);

    // 更新轨道相机控制器的初始状态
    orbitCamera.focus.set(target[0], target[1], target[2]);
    const distance = new pc.Vec3(pos[0] - target[0], pos[1] - target[1], pos[2] - target[2]).length();
    orbitCamera.distance = distance;
}
```

#### 自动相机定位
```javascript
// 如果没有设置初始相机位置，使用splat的边界框来计算合适的相机位置
const aabb = splatEntity.gsplat.instance.splat.aabb;
const center = aabb.center;
const size = aabb.halfExtents.length() * 2;

// 设置相机位置为边界框中心前方
const distance = size * 1.5;
camera.setPosition(center.x, center.y, center.z + distance);
camera.lookAt(center);
```

### 📋 **支持的设置参数**

1. **相机设置**:
   - `camera.position`: 初始相机位置 [x, y, z]
   - `camera.target`: 相机目标点 [x, y, z]
   - `camera.fov`: 相机视野角度

2. **背景设置**:
   - `background.color`: 背景颜色 [r, g, b]

3. **动画设置**:
   - `animTracks`: 动画轨道数组
   - 自动检测动画持续时间

### 🛡️ **错误处理**

实现了完善的错误处理机制：
- 如果 splat 数据加载失败，会显示立方体占位符
- 如果出现异常，会有详细的错误日志
- 保证预览界面始终可用

### ✅ **完整功能列表**

1. **真实数据渲染**: 加载并渲染实际的 3D Gaussian Splat 数据
2. **参数完全应用**: 使用所有 publishSettings 中的配置
3. **智能相机定位**: 自动计算最佳相机位置（如果未设置）
4. **相机控制**: 轨道、飞行、动画三种模式
5. **动画支持**: 完整的动画播放和控制
6. **错误恢复**: 优雅的错误处理和回退机制

## 总结

本实现成功将 Unicity 预览从简单的 dialog 转换为功能丰富的独立页面，提供了更好的用户体验和更强大的预览功能。通过集成 supersplat-viewer 的功能和实际的 splat 数据加载，用户可以在发布前更全面地预览和测试他们的 3D 场景，确保发布的内容符合预期。
