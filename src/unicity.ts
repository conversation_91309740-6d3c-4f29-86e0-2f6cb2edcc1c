import { Events } from './events';
import { getUnicityUser, UnicityProject, UnicityPublishSettings, UnicityUser } from './unicity-service';

/**
 * 注册Unicity相关的事件
 */
const registerUnicityEvents = (events: Events) => {
    /**
     * 检查Unicity功能是否启用
     * 目前简单地检查用户是否已登录
     */
    events.function('unicity.enabled', async () => {
        return !!(await getUnicityUser());
    });

    /**
     * 获取当前登录的Unicity用户
     */
    events.function('unicity.getUser', async (): Promise<UnicityUser | null> => {
        return await getUnicityUser();
    });

     /**
     * 获取本次项目Id
     */
    events.function('unicity.getProjectId', async (): Promise<string | null> => {
        // 从 URL 中获取 projectId 参数
        const urlParams = new URLSearchParams(window.location.search);
        const projectId = urlParams.get('projectId'); // 获取传递的 projectId 参数
        return projectId; // 返回 projectId，如果没有则返回 null
    });


    events.function('unicity.getPublishSettings', async(): Promise<UnicityPublishSettings | null> => {
        const defaultPublishSettings: UnicityPublishSettings = {
            projectId: '5',
            serializeSettings: {
                maxSHBands: 3,
                minOpacity: 1 / 255,
                removeInvalid: true
            },
            experienceSettings: {
                camera: {
                    fov: 50,
                    position: null,
                    target: null,
                    startAnim: 'none',
                    animTrack: null
                },
                background: {
                    color: [0, 0, 0]
                },
                animTracks: []
            }
        };

        return defaultPublishSettings;
    })
};

export { registerUnicityEvents };
