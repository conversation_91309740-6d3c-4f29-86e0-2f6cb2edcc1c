import { path, Vec3 } from 'playcanvas';

import { <PERSON><PERSON><PERSON>rop<PERSON>and<PERSON> } from './drop-handler';
import { ElementType } from './element';
import { Events } from './events';
import { Scene } from './scene';
import { Writer, DownloadWriter, FileStreamWriter, BufferWriter } from './serialize/writer';
import { Splat } from './splat';
import { serializePly, serializePlyCompressed, SerializeSettings, serializeSplat, serializeViewer, ViewerExportSettings } from './splat-serialize';
import { localize } from './ui/localization';
import { UnicityPublishSettings, getUnicityUser, uploadToUnicity } from './unicity-service';

// ts compiler and vscode find this type, but eslint does not
type FilePickerAcceptType = unknown;

interface RemoteStorageDetails {
    method: string;
    url: string;
}

type ExportType = 'ply' | 'compressed-ply' | 'splat' | 'viewer';

interface SceneWriteOptions {
    type: ExportType;
    filename?: string;
    stream?: FileSystemWritableFileStream;
    viewerExportSettings?: ViewerExportSettings
}

const filePickerTypes: { [key: string]: FilePickerAcceptType } = {
    'ply': {
        description: 'Gaussian Splat PLY File',
        accept: {
            'application/ply': ['.ply']
        }
    },
    'compressed-ply': {
        description: 'Compressed Gaussian Splat PLY File',
        accept: {
            'application/ply': ['.ply']
        }
    },
    'splat': {
        description: 'Gaussian Splat File',
        accept: {
            'application/x-gaussian-splat': ['.splat']
        }
    },
    'htmlViewer': {
        description: 'Viewer HTML',
        accept: {
            'text/html': ['.html']
        }
    },
    'packageViewer': {
        description: 'Viewer ZIP',
        accept: {
            'application/zip': ['.zip']
        }
    }
};

let fileHandle: FileSystemFileHandle = null;

const vec = new Vec3();

// download the data to the given filename
const download = (filename: string, data: Uint8Array) => {
    const blob = new Blob([data], { type: 'octet/stream' });
    const url = window.URL.createObjectURL(blob);

    const lnk = document.createElement('a');
    lnk.download = filename;
    lnk.href = url;

    // create a "fake" click-event to trigger the download
    if (document.createEvent) {
        const e = document.createEvent('MouseEvents');
        e.initMouseEvent('click', true, true, window,
            0, 0, 0, 0, 0, false, false, false,
            false, 0, null);
        lnk.dispatchEvent(e);
    } else {
        // @ts-ignore
        lnk.fireEvent?.('onclick');
    }

    window.URL.revokeObjectURL(url);
};

const loadCameraPoses = async (url: string, filename: string, events: Events) => {
    const response = await fetch(url);
    const json = await response.json();
    if (json.length > 0) {
        // calculate the average position of the camera poses
        const ave = new Vec3(0, 0, 0);
        json.forEach((pose: any) => {
            vec.set(pose.position[0], pose.position[1], pose.position[2]);
            ave.add(vec);
        });
        ave.mulScalar(1 / json.length);

        // sort entries by trailing number if it exists
        const sorter = (a: any, b: any) => {
            const avalue = a.id ?? a.img_name?.match(/\d*$/)?.[0];
            const bvalue = b.id ?? b.img_name?.match(/\d*$/)?.[0];
            return (avalue && bvalue) ? parseInt(avalue, 10) - parseInt(bvalue, 10) : 0;
        };

        json.sort(sorter).forEach((pose: any, i: number) => {
            if (pose.hasOwnProperty('position') && pose.hasOwnProperty('rotation')) {
                const p = new Vec3(pose.position);
                const z = new Vec3(pose.rotation[0][2], pose.rotation[1][2], pose.rotation[2][2]);

                const dot = vec.sub2(ave, p).dot(z);
                vec.copy(z).mulScalar(dot).add(p);

                events.fire('camera.addPose', {
                    name: pose.img_name ?? `${filename}_${i}`,
                    frame: i,
                    position: new Vec3(-p.x, -p.y, p.z),
                    target: new Vec3(-vec.x, -vec.y, vec.z)
                });
            }
        });
    }
};

// initialize file handler events
const initFileHandler = (scene: Scene, events: Events, dropTarget: HTMLElement, remoteStorageDetails: RemoteStorageDetails) => {

    // returns a promise that resolves when the file is loaded
    const handleImport = async (url: string, filename?: string, animationFrame = false) => {
        try {
            if (!filename) {
                // extract filename from url if one isn't provided
                try {
                    filename = new URL(url, document.baseURI).pathname.split('/').pop();
                } catch (e) {
                    filename = url;
                }
            }

            const lowerFilename = (filename || url).toLowerCase();
            if (lowerFilename.endsWith('.ssproj')) {
                await events.invoke('doc.dropped', new File([await (await fetch(url)).blob()], filename));
            } else if (lowerFilename.endsWith('.ply') || lowerFilename.endsWith('.splat') || (lowerFilename === 'meta.json')) {
                const model = await scene.assetLoader.loadModel({ url, filename, animationFrame });
                scene.add(model);
                return model;
            } else if (lowerFilename.endsWith('.json')) {
                await loadCameraPoses(url, filename, events);
            } else {
                throw new Error('Unsupported file type');
            }
        } catch (error) {
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('popup.error-loading'),
                message: `${error.message ?? error} while loading '${filename}'`
            });
        }
    };

    events.function('import', (url: string, filename?: string, animationFrame = false) => {
        return handleImport(url, filename, animationFrame);
    });

    // create a file selector element as fallback when showOpenFilePicker isn't available
    let fileSelector: HTMLInputElement;
    if (!window.showOpenFilePicker) {
        fileSelector = document.createElement('input');
        fileSelector.setAttribute('id', 'file-selector');
        fileSelector.setAttribute('type', 'file');
        fileSelector.setAttribute('accept', '.ply,.splat');
        fileSelector.setAttribute('multiple', 'true');

        fileSelector.onchange = async () => {
            const files = fileSelector.files;
            for (let i = 0; i < files.length; i++) {
                const file = fileSelector.files[i];
                const url = URL.createObjectURL(file);
                await handleImport(url, file.name);
                URL.revokeObjectURL(url);
            }
        };
        document.body.append(fileSelector);
    }

    // create the file drag & drop handler
    CreateDropHandler(dropTarget, async (entries, shift) => {
        // document load, only support a single file drop
        if (entries.length === 1 && entries[0].file?.name?.toLowerCase().endsWith('.ssproj')) {
            await events.invoke('doc.dropped', entries[0].file);
            return;
        }

        // filter out non supported extensions
        entries = entries.filter((entry) => {
            const name = entry.file?.name;
            if (!name) return false;
            const lowerName = name.toLowerCase();
            return lowerName.endsWith('.ply') || lowerName.endsWith('.splat') || lowerName.endsWith('.json');
        });

        if (entries.length === 0) {
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('popup.error-loading'),
                message: localize('popup.drop-files')
            });
        } else {
            // determine if all files share a common filename prefix followed by
            // a frame number, e.g. "frame0001.ply", "frame0002.ply", etc.
            const isSequence = () => {
                // eslint-disable-next-line regexp/no-super-linear-backtracking
                const regex = /(.*?)(\d+)(?:\.compressed)?\.ply$/;
                const baseMatch = entries[0].file.name?.toLowerCase().match(regex);
                if (!baseMatch) {
                    return false;
                }

                for (let i = 1; i < entries.length; i++) {
                    const thisMatch = entries[i].file.name?.toLowerCase().match(regex);
                    if (!thisMatch || thisMatch[1] !== baseMatch[1]) {
                        return false;
                    }
                }

                return true;
            };

            if (entries.length > 1 && isSequence()) {
                events.fire('plysequence.setFrames', entries.map(e => e.file));
                events.fire('timeline.frame', 0);
            } else {
                for (let i = 0; i < entries.length; i++) {
                    const entry = entries[i];
                    const url = URL.createObjectURL(entry.file);
                    await handleImport(url, entry.filename);
                    URL.revokeObjectURL(url);
                }
            }
        }
    });

    // get the list of visible splats containing gaussians
    const getSplats = () => {
        return (scene.getElementsByType(ElementType.splat) as Splat[])
        .filter(splat => splat.visible)
        .filter(splat => splat.numSplats > 0);
    };

    events.function('scene.allSplats', () => {
        return (scene.getElementsByType(ElementType.splat) as Splat[]);
    });

    events.function('scene.splats', () => {
        return getSplats();
    });

    events.function('scene.empty', () => {
        return getSplats().length === 0;
    });

    events.function('scene.import', async () => {
        if (fileSelector) {
            fileSelector.click();
        } else {
            try {
                const handles = await window.showOpenFilePicker({
                    id: 'SuperSplatFileOpen',
                    multiple: true,
                    types: [filePickerTypes.ply, filePickerTypes.splat]
                });
                for (let i = 0; i < handles.length; i++) {
                    const handle = handles[i];
                    const file = await handle.getFile();
                    const url = URL.createObjectURL(file);
                    await handleImport(url, file.name);
                    URL.revokeObjectURL(url);

                    if (i === 0) {
                        fileHandle = handle;
                    }
                }
            } catch (error) {
                if (error.name !== 'AbortError') {
                    console.error(error);
                }
            }
        }
    });
    //TODO: 添加对在线文件的支持
    events.function('scene.importOnline', async (url?: string) => {
        try {
            if (!url) {
                url = prompt('请输入 .splat 或 .ply 文件的在线 URL:');
                if (!url) {
                    console.warn('未提供 URL');
                    return;
                }
            }
            // 验证 URL 是否有效
            const lowerFilename = url.toLowerCase();
            if (!lowerFilename.endsWith('.ply') && !lowerFilename.endsWith('.splat')) {
                throw new Error('仅支持 .ply 和 .splat 文件');
            }

            // 加载文件并处理
            await handleImport(url, url.split('/').pop());
            console.log('文件加载成功:', url);
        } catch (error) {
            console.error('加载在线文件失败:', error);
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('popup.error-loading'),
                message: `${error.message ?? error}`
            });
        }

    });

    //TODO:添加在线json的支持
    events.function('scene.LoadAndParseJson', async (url?: string) => {
        try {
            if (!url) {
                url = prompt('请输入在线相机位姿文件的 URL:');
                if (!url) {
                    console.warn('未提供 URL');
                    return;
                }
            }
            // 验证 URL 是否有效
            const lowerFilename = url.toLowerCase();
            if (!lowerFilename.endsWith('.json')) {
                throw new Error('仅支持 .json 文件');
            }

            // 加载文件并处理
            await loadCameraPoses(url, url.split('/').pop(), events);
            console.log('相机位姿加载成功:', url);
        } catch (error) {
            console.error('加载在线相机位姿文件失败:', error);
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('popup.error-loading'),
                message: `${error.message ?? error}`
            });
        }
    });
    // open a folder
    events.function('scene.openAnimation', async () => {
        try {
            const handle = await window.showDirectoryPicker({
                id: 'SuperSplatFileOpenAnimation',
                mode: 'readwrite'
            });

            if (handle) {
                const files = [];
                for await (const value of handle.values()) {
                    if (value.kind === 'file') {
                        const file = await value.getFile();
                        if (file.name.toLowerCase().endsWith('.ply')) {
                            files.push(file);
                        }
                    }
                }
                events.fire('plysequence.setFrames', files);
                events.fire('timeline.frame', 0);
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error(error);
            }
        }
    });

    events.function('scene.export', async (type: ExportType, outputFilename: string = null, exportType: 'export' | 'saveAs' = 'export') => {
        const extensions = {
            'ply': '.ply',
            'compressed-ply': '.compressed.ply',
            'splat': '.splat',
            'viewer': '-viewer.html'
        };

        const removeExtension = (filename: string) => {
            return filename.substring(0, filename.length - path.getExtension(filename).length);
        };

        const replaceExtension = (filename: string, extension: string) => {
            return `${removeExtension(filename)}${extension}`;
        };

        const splats = getSplats();
        const splat = splats[0];
        let filename = outputFilename ?? replaceExtension(splat.filename, extensions[type]);

        const hasFilePicker = window.showSaveFilePicker;

        let viewerExportSettings;
        if (type === 'viewer') {
            // show viewer export options
            viewerExportSettings = await events.invoke('show.viewerExportPopup', hasFilePicker ? null : filename);

            // return if user cancelled
            if (!viewerExportSettings) {
                return;
            }

            if (hasFilePicker) {
                filename = replaceExtension(filename, viewerExportSettings.type === 'html' ? '.html' : '.zip');
            } else {
                filename = viewerExportSettings.filename;
            }
        }

        if (hasFilePicker) {
            try {
                const filePickerType = type === 'viewer' ? (viewerExportSettings.type === 'html' ? filePickerTypes.htmlViewer : filePickerTypes.packageViewer) : filePickerTypes[type];

                const fileHandle = await window.showSaveFilePicker({
                    id: 'SuperSplatFileExport',
                    types: [filePickerType],
                    suggestedName: filename
                });
                await events.invoke('scene.write', {
                    type,
                    stream: await fileHandle.createWritable(),
                    viewerExportSettings
                });
            } catch (error) {
                if (error.name !== 'AbortError') {
                    console.error(error);
                }
            }
        } else {
            await events.invoke('scene.write', { type, filename, viewerExportSettings });
        }
    });

    const writeScene = async (type: ExportType, writer: Writer, viewerExportSettings?: ViewerExportSettings) => {
        const splats = getSplats();
        const events = splats[0].scene.events;

        const serializeSettings: SerializeSettings = {
            maxSHBands: events.invoke('view.bands')
        };

        switch (type) {
            case 'ply':
                await serializePly(splats, serializeSettings, writer);
                break;
            case 'compressed-ply':
                serializeSettings.minOpacity = 1 / 255;
                serializeSettings.removeInvalid = true;
                await serializePlyCompressed(splats, serializeSettings, writer);
                break;
            case 'splat':
                await serializeSplat(splats, serializeSettings, writer);
                break;
            case 'viewer':
                await serializeViewer(splats, viewerExportSettings, writer);
                break;
        }
    };

    events.function('scene.write', async (options: SceneWriteOptions) => {
        events.fire('startSpinner');

        try {
            // setTimeout so spinner has a chance to activate
            await new Promise<void>((resolve) => {
                setTimeout(resolve);
            });

            const { stream, filename, type, viewerExportSettings } = options;
            const writer = stream ? new FileStreamWriter(stream) : new DownloadWriter(filename);

            await writeScene(type, writer, viewerExportSettings);
            await writer.close();
        } catch (error) {
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('popup.error-loading'),
                message: `${error.message ?? error} while saving file`
            });
        } finally {
            events.fire('stopSpinner');
        }
    });

    /**
     * 发布场景到Unicity云服务
     * 导出.splat文件和settings.json文件，并上传到Unicity云服务
     */
    events.function('scene.publishUnicity', async (publishSettings: UnicityPublishSettings) => {
        // 检查用户是否已登录
        const user = await getUnicityUser();
        if (!user) {
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('publish.failed'),
                message: localize('publish.please-log-in')
            });
            return false;
        }

        try {
            events.fire('startSpinner');

            // 延迟一下，让spinner有机会显示
            await new Promise<void>((resolve) => {
                setTimeout(resolve, 10);
            });

            const splats = getSplats();
            if (splats.length === 0) {
                throw new Error(localize('scene.empty'));
            }

            // 1. 序列化.splat文件
            const splatWriter = new BufferWriter();
            await serializeSplat(splats, publishSettings.serializeSettings, splatWriter);
            const splatBuffer = splatWriter.close();

            // 2. 创建settings.json内容
            const settingsJson = JSON.stringify(publishSettings.experienceSettings, null, 4);

            // 3. 上传到Unicity云服务
            const response = await uploadToUnicity(
                splatBuffer,
                settingsJson,
                publishSettings,
                user
            );

            if (!response) {
                await events.invoke('showPopup', {
                    type: 'error',
                    header: localize('publish.failed'),
                    message: localize('publish.please-try-again')
                });
            } else {
                await events.invoke('showPopup', {
                    type: 'info',
                    header: localize('publish.succeeded'),
                    message: localize('publish.message'),
                    link: response.url
                });
            }

            return true;
        } catch (error) {
            await events.invoke('showPopup', {
                type: 'error',
                header: localize('publish.failed'),
                message: `${error.message ?? error}`
            });
            return false;
        } finally {
            events.fire('stopSpinner');
        }
    });

    // 生成增强的Unicity预览，包含viewer功能
    events.function('scene.generateUnicityPreviewWithViewer', async (publishSettings: UnicityPublishSettings) => {
        events.fire('startSpinner');

        try {
            const splats = getSplats();
            if (splats.length === 0) {
                throw new Error(localize('scene.empty'));
            }

            // 序列化.splat文件
            const splatWriter = new BufferWriter();
            await serializeSplat(splats, publishSettings.serializeSettings, splatWriter);
            const splatBuffer = splatWriter.close();

            // 将splat数据转换为base64
            //const splatBase64 = btoa(String.fromCharCode(...splatBuffer));
            const splatBase64 = btoa([...new Uint8Array(splatBuffer)].map(b => String.fromCharCode(b)).join(''));


            // 创建settings.json内容
            const settingsJson = JSON.stringify(publishSettings.experienceSettings, null, 2);

            // 创建增强的viewer HTML，包含完整的相机控制和动画功能
            const viewerHtml = `
<!DOCTYPE html>
<html lang="en">
    <head>
        <title>Unicity Preview</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
        <style>
            body, html {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
                overflow: hidden;
                background-color: #000;
                font-family: Arial, sans-serif;
            }
            pc-app {
                width: 100%;
                height: 100%;
                display: block;
            }
            #loading {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: #000;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            }
            #loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #333;
                border-top: 4px solid #ff6600;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 20px;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            #loading-text {
                color: #fff;
                font-size: 16px;
            }
            .preview-notice {
                position: absolute;
                top: 10px;
                left: 10px;
                background-color: rgba(255, 102, 0, 0.9);
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                z-index: 100;
            }
        </style>
        <script type="importmap">
        {
            "imports": {
                "playcanvas": "https://cdn.jsdelivr.net/npm/playcanvas@2.7.6/build/playcanvas.mjs"
            }
        }
        </script>
        <script type="module">
            import * as pc from 'playcanvas';

            // 设置数据
            const settings = ${settingsJson};
            const splatData = atob('${splatBase64}');

            // 创建应用
            const canvas = document.createElement('canvas');
            document.body.appendChild(canvas);

            const app = new pc.Application(canvas, {
                mouse: new pc.Mouse(canvas),
                touch: new pc.TouchDevice(canvas),
                keyboard: new pc.Keyboard(window),
                graphicsDeviceOptions: {
                    antialias: true,
                    alpha: false
                }
            });

            // 设置画布大小
            app.setCanvasFillMode(pc.FILLMODE_FILL_WINDOW);
            app.setCanvasResolution(pc.RESOLUTION_AUTO);

            // 创建相机
            const camera = new pc.Entity('camera');
            camera.addComponent('camera', {
                clearColor: new pc.Color(settings.background?.color?.[0] || 0,
                                       settings.background?.color?.[1] || 0,
                                       settings.background?.color?.[2] || 0),
                fov: settings.camera?.fov || 75
            });
            app.root.addChild(camera);

            // 创建光源
            const light = new pc.Entity('light');
            light.addComponent('light', {
                type: pc.LIGHTTYPE_DIRECTIONAL,
                color: new pc.Color(1, 1, 1),
                intensity: 1
            });
            light.setEulerAngles(45, 30, 0);
            app.root.addChild(light);

            // 状态管理
            const state = {
                cameraMode: 'orbit',
                animationTime: 0,
                animationDuration: 0,
                animationPaused: true,
                hasAnimation: false
            };

            // 相机控制器
            let orbitCamera, flyCamera, animCamera;
            let currentController;

            // 初始化相机控制器
            const initCameraControllers = () => {
                // 轨道相机控制器
                orbitCamera = {
                    focus: new pc.Vec3(0, 0, 0),
                    distance: 5,
                    pitch: 0,
                    yaw: 0,
                    update: function(dt) {
                        const position = new pc.Vec3();
                        position.x = this.focus.x + this.distance * Math.sin(this.yaw) * Math.cos(this.pitch);
                        position.y = this.focus.y + this.distance * Math.sin(this.pitch);
                        position.z = this.focus.z + this.distance * Math.cos(this.yaw) * Math.cos(this.pitch);

                        camera.setPosition(position);
                        camera.lookAt(this.focus);
                    },
                    reset: function() {
                        this.focus.set(0, 0, 0);
                        this.distance = 5;
                        this.pitch = 0;
                        this.yaw = 0;
                    }
                };

                // 飞行相机控制器
                flyCamera = {
                    position: new pc.Vec3(0, 0, 5),
                    rotation: new pc.Vec3(0, 0, 0),
                    speed: 2,
                    update: function(dt) {
                        camera.setPosition(this.position);
                        camera.setEulerAngles(this.rotation);
                    },
                    reset: function() {
                        this.position.set(0, 0, 5);
                        this.rotation.set(0, 0, 0);
                    }
                };

                // 动画相机控制器
                animCamera = {
                    tracks: settings.animTracks || [],
                    currentTrack: null,
                    time: 0,
                    update: function(dt) {
                        if (this.currentTrack && !state.animationPaused) {
                            this.time += dt;
                            if (this.time >= state.animationDuration) {
                                this.time = 0; // 循环播放
                            }
                            state.animationTime = this.time;

                            // 发送时间更新消息
                            window.parent.postMessage({
                                type: 'animationTimeUpdate',
                                data: { time: this.time }
                            }, '*');
                        }
                    },
                    reset: function() {
                        this.time = 0;
                        state.animationTime = 0;
                    }
                };

                // 检查是否有动画
                if (settings.animTracks && settings.animTracks.length > 0) {
                    state.hasAnimation = true;
                    state.animationDuration = Math.max(...settings.animTracks.map(track => track.duration));
                    animCamera.currentTrack = settings.animTracks[0];
                }

                // 设置默认控制器
                currentController = orbitCamera;
            };

            // 切换相机模式
            const setCameraMode = (mode) => {
                state.cameraMode = mode;
                switch (mode) {
                    case 'orbit':
                        currentController = orbitCamera;
                        break;
                    case 'fly':
                        currentController = flyCamera;
                        break;
                    case 'anim':
                        currentController = animCamera;
                        break;
                }
            };

            // 鼠标控制
            let isMouseDown = false;
            let lastMouseX = 0;
            let lastMouseY = 0;

            canvas.addEventListener('mousedown', (e) => {
                isMouseDown = true;
                lastMouseX = e.clientX;
                lastMouseY = e.clientY;
            });

            canvas.addEventListener('mouseup', () => {
                isMouseDown = false;
            });

            canvas.addEventListener('mousemove', (e) => {
                if (!isMouseDown) return;

                const deltaX = e.clientX - lastMouseX;
                const deltaY = e.clientY - lastMouseY;

                if (state.cameraMode === 'orbit') {
                    orbitCamera.yaw += deltaX * 0.01;
                    orbitCamera.pitch += deltaY * 0.01;
                    orbitCamera.pitch = Math.max(-Math.PI/2, Math.min(Math.PI/2, orbitCamera.pitch));
                } else if (state.cameraMode === 'fly') {
                    flyCamera.rotation.y += deltaX * 0.1;
                    flyCamera.rotation.x += deltaY * 0.1;
                    flyCamera.rotation.x = Math.max(-90, Math.min(90, flyCamera.rotation.x));
                }

                lastMouseX = e.clientX;
                lastMouseY = e.clientY;
            });

            canvas.addEventListener('wheel', (e) => {
                e.preventDefault();
                if (state.cameraMode === 'orbit') {
                    orbitCamera.distance += e.deltaY * 0.01;
                    orbitCamera.distance = Math.max(0.1, orbitCamera.distance);
                }
            });

            // 监听来自父窗口的消息
            window.addEventListener('message', (event) => {
                const { type, data } = event.data;

                switch (type) {
                    case 'initialize':
                        setCameraMode(data.cameraMode);
                        state.animationPaused = data.animationPaused;
                        break;
                    case 'setCameraMode':
                        setCameraMode(data);
                        break;
                    case 'reset':
                        currentController.reset();
                        break;
                    case 'playAnimation':
                        state.animationPaused = false;
                        break;
                    case 'pauseAnimation':
                        state.animationPaused = true;
                        break;
                    case 'setAnimationTime':
                        state.animationTime = data;
                        if (animCamera.currentTrack) {
                            animCamera.time = data;
                        }
                        break;
                }
            });

            // 加载splat数据
            const loadSplatData = async () => {
                try {
                    // 这里应该加载实际的splat数据
                    // 由于这是预览，我们创建一个简单的立方体作为占位符
                    const box = new pc.Entity('box');
                    box.addComponent('render', {
                        type: 'box',
                        material: new pc.StandardMaterial()
                    });
                    app.root.addChild(box);

                    // 初始化相机控制器
                    initCameraControllers();

                    // 发送viewer就绪消息
                    window.parent.postMessage({
                        type: 'viewerReady'
                    }, '*');

                    // 发送动画信息
                    window.parent.postMessage({
                        type: 'animationInfo',
                        data: {
                            hasAnimation: state.hasAnimation,
                            duration: state.animationDuration
                        }
                    }, '*');

                } catch (error) {
                    console.error('加载splat数据失败:', error);
                }
            };

            // 更新循环
            app.on('update', (dt) => {
                if (currentController) {
                    currentController.update(dt);
                }
            });

            // 启动应用
            app.start();

            // 加载数据
            loadSplatData().then(() => {
                // 隐藏加载界面
                document.getElementById('loading').style.display = 'none';
            });
        </script>
    </head>
    <body>
        <div id="loading">
            <div id="loading-spinner"></div>
            <div id="loading-text">加载预览中...</div>
        </div>
        <div class="preview-notice">Unicity 预览模式</div>
    </body>
</html>`;

            return viewerHtml;
        } catch (error) {
            console.error('生成增强预览失败:', error);
            throw error;
        } finally {
            events.fire('stopSpinner');
        }
    });
};

export { initFileHandler };
