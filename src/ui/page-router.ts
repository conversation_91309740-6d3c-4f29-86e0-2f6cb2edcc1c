import { Container } from 'pcui';
import { Events } from '../events';

export type PageType = 'editor' | 'preview';

export interface Page {
    id: string;
    container: Container;
    onShow?: () => void;
    onHide?: () => void;
}

/**
 * 简单的页面路由管理器
 */
class PageRouter {
    private events: Events;
    private pages: Map<PageType, Page> = new Map();
    private currentPage: PageType | null = null;
    private appContainer: Container;

    constructor(events: Events, appContainer: Container) {
        this.events = events;
        this.appContainer = appContainer;
    }

    /**
     * 注册页面
     */
    registerPage(type: PageType, page: Page) {
        this.pages.set(type, page);
        
        // 初始时隐藏所有页面
        page.container.hidden = true;
        this.appContainer.append(page.container);
    }

    /**
     * 导航到指定页面
     */
    async navigateTo(pageType: PageType, data?: any): Promise<any> {
        const targetPage = this.pages.get(pageType);
        if (!targetPage) {
            console.error(`Page ${pageType} not found`);
            return;
        }

        // 隐藏当前页面
        if (this.currentPage) {
            const currentPageObj = this.pages.get(this.currentPage);
            if (currentPageObj) {
                currentPageObj.container.hidden = true;
                if (currentPageObj.onHide) {
                    currentPageObj.onHide();
                }
            }
        }

        // 显示目标页面
        targetPage.container.hidden = false;
        if (targetPage.onShow) {
            targetPage.onShow();
        }

        this.currentPage = pageType;

        // 触发页面切换事件
        this.events.fire('page.changed', pageType, data);

        return data;
    }

    /**
     * 获取当前页面
     */
    getCurrentPage(): PageType | null {
        return this.currentPage;
    }

    /**
     * 返回上一页
     */
    goBack(): void {
        if (this.currentPage === 'preview') {
            this.navigateTo('editor');
        }
    }

    /**
     * 检查是否在指定页面
     */
    isCurrentPage(pageType: PageType): boolean {
        return this.currentPage === pageType;
    }
}

export { PageRouter };
