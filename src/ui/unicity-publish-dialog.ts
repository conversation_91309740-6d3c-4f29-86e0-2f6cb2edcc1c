import { BooleanInput, Button, ColorPicker, Container, Element, Label, SelectInput, SliderInput, TextAreaInput, TextInput } from 'pcui';

import { Pose } from '../camera-poses';
import { Events } from '../events';
import { localize } from './localization';
import { AnimTrack, ExperienceSettings } from '../splat-serialize';
import { UnicityPublishSettings, UnicityProject } from '../unicity-service';
import sceneExport from './svg/export.svg';

const createSvg = (svgString: string, args = {}) => {
    const decodedStr = decodeURIComponent(svgString.substring('data:image/svg+xml,'.length));
    return new Element({
        dom: new DOMParser().parseFromString(decodedStr, 'image/svg+xml').documentElement,
        ...args
    });
};

/**
 * Unicity发布设置对话框
 */
class UnicityPublishDialog extends Container {
    show: (previousSettings?: UnicityPublishSettings) => Promise<UnicityPublishSettings>;
    hide: () => void;
    destroy: () => void;

    constructor(events: Events, args = {}) {
        args = {
            id: 'unicity-publish-dialog',
            hidden: true,
            tabIndex: -1,
            ...args
        };

        super(args);

        const dialog = new Container({
            id: 'dialog'
        });

        let onCancel: () => void;
        let onOK: () => void;

        // 键盘事件处理
        const keydown = (event: KeyboardEvent) => {
            switch (event.key) {
                case 'Escape':
                    onCancel();
                    break;
                case 'Enter':
                    if (!event.shiftKey) onOK();
                    break;
                default:
                    event.stopPropagation();
                    break;
            }
        };

        // 标题
        const header = new Container({
            id: 'header'
        });

        const headerText = new Label({
            id: 'header',
            text: localize('unicity.header')
        });

        header.append(createSvg(sceneExport, {
            id: 'icon'
        }));

        header.append(headerText);

        // 项目选择
        const projectRow = new Container({
            class: 'row'
        });

        const projectLabel = new Label({
            class: 'label',
            text: localize('unicity.project')
        });

        const projectIdLabel = new Label({
            class: 'label-right',
            text: ''
        });

        projectRow.append(projectLabel);
        projectRow.append(projectIdLabel);

        // 相机起始位置
        const startRow = new Container({
            class: 'row'
        });

        const startLabel = new Label({
            class: 'label',
            text: localize('export.start-position')
        });

        const startSelect = new SelectInput({
            class: 'select',
            defaultValue: 'viewport',
            options: [
                { v: 'default', t: localize('export.default') },
                { v: 'viewport', t: localize('export.viewport') },
                { v: 'pose', t: localize('export.pose-camera') }
            ]
        });

        startRow.append(startLabel);
        startRow.append(startSelect);

        // 动画
        const animationRow = new Container({
            class: 'row'
        });

        const animationLabel = new Label({
            class: 'label',
            text: localize('export.animation')
        });

        const animationSelect = new SelectInput({
            class: 'select',
            defaultValue: 'none',
            options: [
                { v: 'none', t: localize('export.animation-none') },
                { v: 'track', t: localize('export.animation-track') }
            ]
        });

        animationRow.append(animationLabel);
        animationRow.append(animationSelect);

        // 背景颜色
        const colorRow = new Container({
            class: 'row'
        });

        const colorLabel = new Label({
            class: 'label',
            text: localize('export.background-color')
        });

        const colorPicker = new ColorPicker({
            class: 'color-picker',
            value: [1, 1, 1, 1]
        });

        colorRow.append(colorLabel);
        colorRow.append(colorPicker);

        // 视场角
        const fovRow = new Container({
            class: 'row'
        });

        const fovLabel = new Label({
            class: 'label',
            text: localize('export.fov')
        });

        const fovSlider = new SliderInput({
            class: 'slider',
            min: 10,
            max: 120,
            precision: 0,
            value: 60
        });

        fovRow.append(fovLabel);
        fovRow.append(fovSlider);

        // 球谐波带
        const bandsRow = new Container({
            class: 'row'
        });

        const bandsLabel = new Label({
            class: 'label',
            text: localize('export.sh-bands')
        });

        const bandsSlider = new SliderInput({
            class: 'slider',
            min: 0,
            max: 3,
            precision: 0,
            value: 3
        });

        bandsRow.append(bandsLabel);
        bandsRow.append(bandsSlider);

        // 内容
        const content = new Container({
            id: 'content'
        });

        content.append(projectRow);
        content.append(startRow);
        content.append(animationRow);
        content.append(colorRow);
        content.append(fovRow);
        content.append(bandsRow);

        // 底部按钮
        const footer = new Container({
            id: 'footer'
        });

        const cancelButton = new Button({
            class: 'button',
            text: localize('publish.cancel')
        });

        const publishButton = new Button({
            class: 'button',
            text: localize('unicity.publish')
        });

        footer.append(cancelButton);
        footer.append(publishButton);

        // 添加所有元素到对话框
        dialog.append(header);
        dialog.append(content);
        dialog.append(footer);

        this.append(dialog);

        // 事件处理
        cancelButton.on('click', () => onCancel());
        publishButton.on('click', () => onOK());

        // 重置UI
        const reset = async (hasPoses: boolean) => {
            const bgClr = events.invoke('bgClr');

            // 尝试获取用户项目列表
            try {
                const user = await events.invoke('unicity.getUser');
                if (user) {
                    const projectId = await events.invoke('unicity.getProjectId');

                    // 清除现有选项
                    projectIdLabel.text = "";

                    // 添加项目选项
                    if (projectId) {
                        projectIdLabel.text = projectId;
                    } else {
                        projectIdLabel.text = localize('unicity.no-projects');
                        // 禁用发布按钮
                        publishButton.enabled = false;
                    }
                }
            } catch (e) {
                console.error('获取项目列表失败:', e);
            }

            startSelect.value = hasPoses ? 'pose' : 'viewport';
            startSelect.disabledOptions = hasPoses ? {} : { 'pose': startSelect.options[2].t };
            animationSelect.value = hasPoses ? 'track' : 'none';
            animationSelect.disabledOptions = hasPoses ? {} : { track: animationSelect.options[1].t };
            colorPicker.value = [bgClr.r, bgClr.g, bgClr.b];
            fovSlider.value = events.invoke('camera.fov');
            bandsSlider.value = events.invoke('view.bands');
        };

        // 显示对话框
        this.show = async (previousSettings?: UnicityPublishSettings) => {
            const frames = events.invoke('timeline.frames');
            const frameRate = events.invoke('timeline.frameRate');

            // 获取相机位置
            const orderedPoses = (events.invoke('camera.poses') as Pose[])
                .slice()
                .filter(p => p.frame >= 0 && p.frame < frames)
                .sort((a, b) => a.frame - b.frame);

            // 如果有之前的设置，使用它们
            if (previousSettings) {
                // 设置相机选项
                if (previousSettings.experienceSettings.camera) {
                    const camera = previousSettings.experienceSettings.camera;
                    if (camera.startAnim) {
                        startSelect.value = camera.startAnim === 'orbit' ? 'viewport' : 
                                            camera.startAnim === 'animTrack' ? 'pose' : 'default';
                    }
                    
                    if (camera.fov) {
                        fovSlider.value = camera.fov;
                    }
                }
                
                // 设置背景颜色
                if (previousSettings.experienceSettings.background && 
                    previousSettings.experienceSettings.background.color) {
                    colorPicker.value = previousSettings.experienceSettings.background.color;
                }
                
                // 设置动画选项
                if (previousSettings.experienceSettings.camera && 
                    previousSettings.experienceSettings.camera.startAnim) {
                    animationSelect.value = previousSettings.experienceSettings.camera.startAnim === 'animTrack' ? 
                                           'track' : 'none';
                }
                
                // 设置SH波段
                if (previousSettings.serializeSettings && 
                    previousSettings.serializeSettings.maxSHBands !== undefined) {
                    bandsSlider.value = previousSettings.serializeSettings.maxSHBands;
                }
            }

            // 重置UI
            await reset(orderedPoses.length > 0);

            this.hidden = false;
            this.dom.addEventListener('keydown', keydown);
            this.dom.focus();

            return new Promise<UnicityPublishSettings>((resolve) => {
                onCancel = () => {
                    resolve(null);
                };

                onOK = () => {
                    // 提取相机起始位置
                    let pose;
                    switch (startSelect.value) {
                        case 'pose':
                            pose = orderedPoses?.[0];
                            break;
                        case 'viewport':
                            pose = events.invoke('camera.getPose');
                            break;
                    }
                    const p = pose?.position;
                    const t = pose?.target;

                    const startAnim = (() => {
                        switch (animationSelect.value) {
                            case 'none': return 'none';
                            case 'track': return 'animTrack';
                        }
                    })();

                    // 提取相机动画
                    const animTracks: AnimTrack[] = [];
                    switch (startAnim) {
                        case 'none':
                            break;
                        case 'animTrack': {
                            // 使用相机位置
                            const times = [];
                            const position = [];
                            const target = [];
                            for (let i = 0; i < orderedPoses.length; ++i) {
                                const p = orderedPoses[i];
                                times.push(p.frame);
                                position.push(p.position.x, p.position.y, p.position.z);
                                target.push(p.target.x, p.target.y, p.target.z);
                            }

                            animTracks.push({
                                name: 'cameraAnim',
                                duration: frames / frameRate,
                                frameRate,
                                target: 'camera',
                                loopMode: 'repeat',
                                interpolation: 'spline',
                                keyframes: {
                                    times,
                                    values: { position, target }
                                }
                            });

                            break;
                        }
                    }

                    // 构建体验设置
                    const experienceSettings: ExperienceSettings = {
                        camera: {
                            fov: fovSlider.value,
                            position: p ? [p.x, p.y, p.z] : null,
                            target: t ? [t.x, t.y, t.z] : null,
                            startAnim,
                            animTrack: startAnim === 'animTrack' ? 'cameraAnim' : null
                        },
                        background: {
                            color: colorPicker.value.slice()
                        },
                        animTracks
                    };

                    const serializeSettings = {
                        maxSHBands: bandsSlider.value,
                        minOpacity: 1 / 255,                    // 移除完全半透明的点
                        removeInvalid: true                     // 移除包含NaN数据的高斯点
                    };

                    const publishSettings: UnicityPublishSettings = {
                        projectId: projectIdLabel.value,
                        serializeSettings,
                        experienceSettings
                    };

                    // 必须选择一个有效的项目ID
                    if (projectIdLabel.value === localize('unicity.no-projects')) {
                        events.invoke('showPopup', {
                            type: 'error',
                            header: localize('publish.failed'),
                            message: localize('unicity.no-projects')
                        });
                        resolve(null);
                        return;
                    }

                    resolve(publishSettings);
                };
            }).finally(() => {
                this.dom.removeEventListener('keydown', keydown);
                this.hide();
            });
        };

        // 隐藏对话框
        this.hide = () => {
            this.hidden = true;
        };

        // 销毁对话框
        this.destroy = () => {
            this.hide();
            super.destroy();
        };
    }

}

export { UnicityPublishDialog };
