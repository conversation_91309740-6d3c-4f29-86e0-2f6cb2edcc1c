import { Button, Container, Element, Label } from 'pcui';

import { Events } from '../events';
import { localize } from './localization';
import { UnicityPublishSettings } from '../unicity-service';
import scenePublish from './svg/publish.svg';

const createSvg = (svgString: string, args = {}) => {
    const decodedStr = decodeURIComponent(svgString.substring('data:image/svg+xml,'.length));
    return new Element({
        dom: new DOMParser().parseFromString(decodedStr, 'image/svg+xml').documentElement,
        ...args
    });
};

/**
 * Unicity预览页面
 */
class UnicityPreview extends Container {
    show: () => Promise<{ action: 'publish' | 'edit' | 'cancel', settings?: UnicityPublishSettings }>;
    hide: () => void;
    destroy: () => void;
    private previewFrame: HTMLIFrameElement;
    private publishSettings: UnicityPublishSettings;
    generatePreview: () => Promise<void>;

    constructor(events: Events, args = {}) {
        args = {
            id: 'unicity-preview-dialog',
            hidden: true,
            tabIndex: -1,
            ...args
        };

        super(args);

        const dialog = new Container({
            id: 'dialog'
        });

        let onCancel: () => void;
        let onPublish: () => void;
        let onEdit: () => void;

        // 键盘事件处理
        const keydown = (event: KeyboardEvent) => {
            switch (event.key) {
                case 'Escape':
                    onCancel();
                    break;
                default:
                    event.stopPropagation();
                    break;
            }
        };

        // 标题
        const header = new Container({
            id: 'header'
        });

        const headerText = new Label({
            id: 'header',
            text: localize('unicity.preview')
        });

        header.append(createSvg(scenePublish, {
            id: 'icon'
        }));

        header.append(headerText);

        // 预览内容
        const content = new Container({
            id: 'preview-content'
        });

        // 创建iframe用于预览
        this.previewFrame = document.createElement('iframe');
        this.previewFrame.id = 'preview-frame';
        this.previewFrame.style.width = '100%';
        this.previewFrame.style.height = '100%';
        this.previewFrame.style.border = 'none';

        const previewElement = new Element({
            dom: this.previewFrame
        });

        content.append(previewElement);

        // 底部按钮
        const footer = new Container({
            id: 'footer'
        });

        const cancelButton = new Button({
            class: 'button',
            text: localize('publish.cancel')
        });

        const editButton = new Button({
            class: 'button',
            text: localize('unicity.edit')
        });

        const publishButton = new Button({
            class: ['button', 'primary'],
            text: localize('unicity.publish')
        });

        footer.append(cancelButton);
        footer.append(editButton);
        footer.append(publishButton);

        // 添加所有元素到对话框
        dialog.append(header);
        dialog.append(content);
        dialog.append(footer);

        this.append(dialog);

        // 事件处理
        cancelButton.on('click', () => onCancel());
        editButton.on('click', () => onEdit());
        publishButton.on('click', () => onPublish());

        // 显示预览
        this.show = async () => {
            // 获取发布设置
            this.publishSettings = await events.invoke('unicity.getPublishSettings');
            
            if (!this.publishSettings) {
                return { action: 'cancel' };
            }

            // 生成预览内容
            await this.generatePreview();

            this.hidden = false;
            this.dom.addEventListener('keydown', keydown);
            this.dom.focus();

            return new Promise<{ action: 'publish' | 'edit' | 'cancel', settings?: UnicityPublishSettings }>((resolve) => {
                onCancel = () => {
                    this.hide();
                    resolve({ action: 'cancel' });
                };

                onEdit = () => {
                    this.hide();
                    resolve({ action: 'edit', settings: this.publishSettings });
                };

                onPublish = () => {
                    this.hide();
                    resolve({ action: 'publish', settings: this.publishSettings });
                };
            }).finally(() => {
                this.dom.removeEventListener('keydown', keydown);
            });
        };

        // 生成预览内容
        this.generatePreview = async () => {
            // 创建临时场景预览
            const previewHtml = await events.invoke('scene.generateUnicityPreview', this.publishSettings);

            console.log('previewHtml: '+previewHtml)
            
            // 将预览数据加载到iframe中
            const blob = new Blob([previewHtml], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            this.previewFrame.src = url;
            
            // 在iframe加载完成后释放URL
            this.previewFrame.onload = () => {
                URL.revokeObjectURL(url);
            };
        };

        // 隐藏对话框
        this.hide = () => {
            this.hidden = true;
            // 清空iframe内容
            this.previewFrame.src = 'about:blank';
        };

        // 销毁对话框
        this.destroy = () => {
            this.hide();
            super.destroy();
        };
    }
}

export { UnicityPreview };