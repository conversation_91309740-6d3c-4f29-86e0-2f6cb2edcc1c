import { Button, Container, Element, Label, SliderInput } from 'pcui';

import { Events } from '../events';
import { localize } from './localization';
import { UnicityPublishSettings } from '../unicity-service';
import scenePublish from './svg/publish.svg';
import backIcon from './svg/arrow.svg';

const createSvg = (svgString: string, args = {}) => {
    const decodedStr = decodeURIComponent(svgString.substring('data:image/svg+xml,'.length));
    return new Element({
        dom: new DOMParser().parseFromString(decodedStr, 'image/svg+xml').documentElement,
        ...args
    });
};

/**
 * Unicity预览页面 - 独立页面版本
 */
class UnicityPreviewPage extends Container {
    show: () => Promise<{ action: 'publish' | 'edit' | 'cancel', settings?: UnicityPublishSettings }>;
    hide: () => void;
    destroy: () => void;
    private previewFrame: HTMLIFrameElement;
    private publishSettings: UnicityPublishSettings;
    private viewerState: any;
    generatePreview: () => Promise<void>;

    constructor(events: Events, args = {}) {
        args = {
            id: 'unicity-preview-page',
            hidden: true,
            ...args
        };

        super(args);

        let onCancel: () => void;
        let onPublish: () => void;
        let onEdit: () => void;

        // 顶部工具栏
        const toolbar = new Container({
            id: 'preview-toolbar'
        });

        // 返回按钮
        const backButton = new Button({
            class: ['button', 'back-button'],
            text: localize('unicity.back-to-edit')
        });

        backButton.dom.prepend(createSvg(backIcon, { class: 'back-icon' }).dom);

        // 标题
        const title = new Label({
            id: 'preview-title',
            text: localize('unicity.preview')
        });

        // 发布按钮
        const publishButton = new Button({
            class: ['button', 'primary', 'publish-button'],
            text: localize('unicity.publish')
        });

        publishButton.dom.prepend(createSvg(scenePublish, { class: 'publish-icon' }).dom);

        toolbar.append(backButton);
        toolbar.append(title);
        toolbar.append(publishButton);

        // 主要内容区域
        const mainContent = new Container({
            id: 'preview-main-content'
        });

        // 预览区域
        const previewContainer = new Container({
            id: 'preview-container'
        });

        // 创建iframe用于预览
        this.previewFrame = document.createElement('iframe');
        this.previewFrame.id = 'preview-frame';
        this.previewFrame.style.width = '100%';
        this.previewFrame.style.height = '100%';
        this.previewFrame.style.border = 'none';

        const previewElement = new Element({
            dom: this.previewFrame
        });

        previewContainer.append(previewElement);

        // 控制面板
        const controlPanel = new Container({
            id: 'preview-control-panel'
        });

        // 相机控制
        const cameraControls = new Container({
            id: 'camera-controls',
            class: 'control-section'
        });

        const cameraLabel = new Label({
            text: localize('camera.controls'),
            class: 'control-label'
        });

        const orbitButton = new Button({
            class: ['button', 'camera-mode-button', 'active'],
            text: localize('camera.orbit'),
            id: 'orbit-mode-btn'
        });

        const flyButton = new Button({
            class: ['button', 'camera-mode-button'],
            text: localize('camera.fly'),
            id: 'fly-mode-btn'
        });

        const animButton = new Button({
            class: ['button', 'camera-mode-button'],
            text: localize('camera.animation'),
            id: 'anim-mode-btn'
        });

        const resetButton = new Button({
            class: ['button', 'reset-button'],
            text: localize('camera.reset')
        });

        cameraControls.append(cameraLabel);
        cameraControls.append(orbitButton);
        cameraControls.append(flyButton);
        cameraControls.append(animButton);
        cameraControls.append(resetButton);

        // 动画控制
        const animationControls = new Container({
            id: 'animation-controls',
            class: 'control-section',
            hidden: true
        });

        const animationLabel = new Label({
            text: localize('animation.controls'),
            class: 'control-label'
        });

        const playButton = new Button({
            class: ['button', 'play-button'],
            text: localize('animation.play')
        });

        const pauseButton = new Button({
            class: ['button', 'pause-button'],
            text: localize('animation.pause'),
            hidden: true
        });

        const timelineSlider = new SliderInput({
            min: 0,
            max: 100,
            value: 0,
            precision: 2
        });

        const timeLabel = new Label({
            text: '0.00s',
            class: 'time-label'
        });

        animationControls.append(animationLabel);
        animationControls.append(playButton);
        animationControls.append(pauseButton);
        animationControls.append(timelineSlider);
        animationControls.append(timeLabel);

        controlPanel.append(cameraControls);
        controlPanel.append(animationControls);

        mainContent.append(previewContainer);
        mainContent.append(controlPanel);

        this.append(toolbar);
        this.append(mainContent);

        // 初始化viewer状态
        this.viewerState = {
            cameraMode: 'orbit',
            animationTime: 0,
            animationDuration: 0,
            animationPaused: true,
            hasAnimation: false
        };

        // 事件处理
        backButton.on('click', () => onEdit());
        publishButton.on('click', () => onPublish());

        // 相机模式切换
        const cameraButtons = [orbitButton, flyButton, animButton];
        const cameraModes = ['orbit', 'fly', 'anim'];

        cameraButtons.forEach((button, index) => {
            button.on('click', () => {
                // 更新按钮状态
                cameraButtons.forEach(btn => btn.class.remove('active'));
                button.class.add('active');

                // 更新相机模式
                this.viewerState.cameraMode = cameraModes[index];
                this.sendMessageToViewer('setCameraMode', cameraModes[index]);

                // 显示/隐藏动画控制
                animationControls.hidden = cameraModes[index] !== 'anim';
            });
        });

        // 重置按钮
        resetButton.on('click', () => {
            this.sendMessageToViewer('reset');
        });

        // 动画控制
        playButton.on('click', () => {
            this.viewerState.animationPaused = false;
            playButton.hidden = true;
            pauseButton.hidden = false;
            this.sendMessageToViewer('playAnimation');
        });

        pauseButton.on('click', () => {
            this.viewerState.animationPaused = true;
            playButton.hidden = false;
            pauseButton.hidden = true;
            this.sendMessageToViewer('pauseAnimation');
        });

        // 时间轴控制
        timelineSlider.on('change', (value: number) => {
            const time = (value / 100) * this.viewerState.animationDuration;
            this.viewerState.animationTime = time;
            timeLabel.text = `${time.toFixed(2)}s`;
            this.sendMessageToViewer('setAnimationTime', time);
        });

        // 监听来自viewer的消息
        window.addEventListener('message', (event) => {
            if (event.source === this.previewFrame.contentWindow) {
                this.handleViewerMessage(event.data);
            }
        });

        // 显示预览页面
        this.show = async () => {
            // 获取发布设置
            this.publishSettings = await events.invoke('unicity.getPublishSettings');
            
            if (!this.publishSettings) {
                return { action: 'cancel' };
            }

            // 生成预览内容
            await this.generatePreview();

            this.hidden = false;

            return new Promise<{ action: 'publish' | 'edit' | 'cancel', settings?: UnicityPublishSettings }>((resolve) => {
                onCancel = () => {
                    this.hide();
                    resolve({ action: 'cancel' });
                };

                onEdit = () => {
                    this.hide();
                    resolve({ action: 'edit', settings: this.publishSettings });
                };

                onPublish = () => {
                    this.hide();
                    resolve({ action: 'publish', settings: this.publishSettings });
                };
            });
        };

        // 生成预览内容
        this.generatePreview = async () => {
            // 创建增强的预览HTML，包含viewer功能
            const previewHtml = await events.invoke('scene.generateUnicityPreviewWithViewer', this.publishSettings);
            
            // 将预览数据加载到iframe中
            const blob = new Blob([previewHtml], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            this.previewFrame.src = url;
            
            // 在iframe加载完成后释放URL
            this.previewFrame.onload = () => {
                URL.revokeObjectURL(url);
                // 初始化viewer状态
                this.initializeViewer();
            };
        };

        // 隐藏预览页面
        this.hide = () => {
            this.hidden = true;
            // 清空iframe内容
            this.previewFrame.src = 'about:blank';
        };

        // 销毁预览页面
        this.destroy = () => {
            this.hide();
            super.destroy();
        };
    }

    // 向viewer发送消息
    private sendMessageToViewer(type: string, data?: any) {
        if (this.previewFrame.contentWindow) {
            this.previewFrame.contentWindow.postMessage({ type, data }, '*');
        }
    }

    // 处理来自viewer的消息
    private handleViewerMessage(message: any) {
        switch (message.type) {
            case 'viewerReady':
                this.initializeViewer();
                break;
            case 'animationInfo':
                this.viewerState.hasAnimation = message.data.hasAnimation;
                this.viewerState.animationDuration = message.data.duration;
                // 更新UI状态
                this.updateAnimationControls();
                break;
            case 'animationTimeUpdate':
                this.viewerState.animationTime = message.data.time;
                this.updateTimeDisplay();
                break;
        }
    }

    // 初始化viewer
    private initializeViewer() {
        this.sendMessageToViewer('initialize', {
            cameraMode: this.viewerState.cameraMode,
            animationPaused: this.viewerState.animationPaused
        });
    }

    // 更新动画控制UI
    private updateAnimationControls() {
        const animationControls = this.dom.querySelector('#animation-controls') as any;
        if (animationControls) {
            animationControls.ui.hidden = !this.viewerState.hasAnimation;
        }
    }

    // 更新时间显示
    private updateTimeDisplay() {
        const timeLabel = this.dom.querySelector('.time-label') as any;
        const timelineSlider = this.dom.querySelector('.pcui-slider') as any;
        
        if (timeLabel) {
            timeLabel.ui.text = `${this.viewerState.animationTime.toFixed(2)}s`;
        }
        
        if (timelineSlider && this.viewerState.animationDuration > 0) {
            const percentage = (this.viewerState.animationTime / this.viewerState.animationDuration) * 100;
            timelineSlider.ui.value = percentage;
        }
    }
}

export { UnicityPreviewPage };
