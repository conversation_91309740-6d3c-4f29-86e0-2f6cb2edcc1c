{"name": "supersplat-viewer", "version": "1.0.1", "author": "PlayCanvas <<EMAIL>>", "homepage": "https://github.com/playcanvas/supersplat-viewer#readme", "description": "Viewer for https://superspl.at", "license": "MIT", "main": "index.js", "files": ["dist"], "keywords": ["playcanvas", "ply", "gaussian", "splat", "viewer"], "repository": {"type": "git", "url": "git+https://github.com/playcanvas/supersplat-viewer.git"}, "bugs": {"url": "https://github.com/playcanvas/supersplat-viewer/issues"}, "scripts": {"build": "rollup -c", "watch": "rollup -c -w", "serve": "serve dist -C", "develop": "concurrently --kill-others \"npm run watch\" \"npm run serve\"", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint src"}, "devDependencies": {"@playcanvas/eslint-config": "^2.0.9", "@playcanvas/web-components": "^0.2.6", "@rollup/plugin-node-resolve": "^16.0.1", "concurrently": "^9.1.2", "eslint": "^9.25.1", "globals": "^16.0.0", "playcanvas": "^2.7.3", "rollup": "^4.40.0", "rollup-plugin-copy": "^3.5.0", "serve": "^14.2.4"}}